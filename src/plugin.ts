import streamDeck, { LogLevel } from "@elgato/streamdeck";

import { MSPAControl } from "./actions/mspa-control";

// Set production log level to INFO for user support and debugging
streamDeck.logger.setLevel(LogLevel.DEBUG);

// Log plugin startup
streamDeck.logger.info("MSpa Plugin starting up...");

// Register the MSPA control action.
streamDeck.actions.registerAction(new MSPAControl());

// Finally, connect to the Stream Deck.
streamDeck.connect();

streamDeck.logger.info("MSpa Plugin initialized successfully");
